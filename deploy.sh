#!/bin/bash

# 多交易所持仓可视化项目一键部署脚本
# 使用方法: ./deploy.sh <ssh_host> [port]
# 例如: ./deploy.sh live-analyze 5000

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    print_error "使用方法: $0 <ssh_host> [port]"
    print_info "例如: $0 live-analyze 5000"
    print_info "支持SSH配置别名和完整地址"
    exit 1
fi

SSH_HOST=$1
PORT=${2:-5000}  # 默认端口5000
PROJECT_NAME="caokong_vz"

print_info "🚀 开始部署到: $SSH_HOST"
print_info "服务端口: $PORT"

# 1. 检查本地文件
print_info "1️⃣ 检查本地项目文件..."
required_files=("app.py" "requirements.txt" "templates/index.html" "static/style.css" "static/script.js" "test_client.py")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "缺少必要文件: $file"
        exit 1
    fi
done
print_success "本地文件检查完成"

# 2. 检查SSH连接
print_info "2️⃣ 测试SSH连接..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $SSH_HOST "echo 'SSH连接成功'" > /dev/null 2>&1; then
    print_error "无法连接到 $SSH_HOST，请检查SSH配置"
    exit 1
fi
print_success "SSH连接正常"

# 3. 获取远程环境信息
print_info "3️⃣ 检测远程环境..."
REMOTE_INFO=$(ssh $SSH_HOST "
    echo \"HOME=\$HOME\"
    echo \"USER=\$USER\"
    echo \"HOSTNAME=\$(hostname)\"
    echo \"PYTHON=\$(which python3 2>/dev/null || which python 2>/dev/null || echo 'none')\"
    echo \"PIP=\$(which pip 2>/dev/null || which pip3 2>/dev/null || echo 'none')\"
")

# 解析远程信息
eval "$REMOTE_INFO"
print_success "远程环境检测完成"
echo "  - 用户: $USER"
echo "  - 主机: $HOSTNAME"
echo "  - Python: $PYTHON"
echo "  - Pip: $PIP"

# 4. 选择部署目录
print_info "4️⃣ 选择部署目录..."
POSSIBLE_DIRS=(
    "$HOME/deploy/$PROJECT_NAME"
    "$HOME/tmp/$PROJECT_NAME"
    "$HOME/$PROJECT_NAME"
)

REMOTE_DIR=""
for dir in "${POSSIBLE_DIRS[@]}"; do
    if ssh $SSH_HOST "mkdir -p \"$dir\" 2>/dev/null"; then
        REMOTE_DIR="$dir"
        break
    fi
done

if [ -z "$REMOTE_DIR" ]; then
    print_error "无法创建部署目录"
    exit 1
fi

print_success "部署目录: $REMOTE_DIR"

# 5. 停止现有服务
print_info "5️⃣ 停止现有服务..."
ssh $SSH_HOST "
    # 停止现有服务
    pkill -f 'python.*app.py' 2>/dev/null || true
    if [ -f '$REMOTE_DIR/app.pid' ]; then
        kill \$(cat '$REMOTE_DIR/app.pid') 2>/dev/null || true
        rm -f '$REMOTE_DIR/app.pid'
    fi
    sleep 2
"
print_success "现有服务已停止"

# 6. 创建目录并上传文件
print_info "6️⃣ 上传项目文件..."
ssh $SSH_HOST "mkdir -p '$REMOTE_DIR'/{templates,static,logs}"

scp -r app.py requirements.txt templates/ static/ test_client.py $SSH_HOST:$REMOTE_DIR/
if [ -f "README.md" ]; then
    scp README.md $SSH_HOST:$REMOTE_DIR/
fi
print_success "文件上传完成"

# 7. 安装依赖和启动服务
print_info "7️⃣ 安装依赖和启动服务..."
ssh $SSH_HOST "
cd '$REMOTE_DIR'

# 创建虚拟环境（如果不存在）
if [ ! -d venv ]; then
    echo '创建Python虚拟环境...'
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
echo '安装Python依赖...'
pip install -r requirements.txt

# 修改端口配置（如果需要）
if [ '$PORT' != '5000' ]; then
    sed -i 's/port=5000/port=$PORT/g' app.py
fi

# 启动服务
echo '启动服务...'
nohup python app.py > logs/service.log 2>&1 &
echo \$! > app.pid

# 等待服务启动
sleep 3

# 检查服务状态
if kill -0 \$(cat app.pid) 2>/dev/null; then
    echo '✅ 服务启动成功'
    echo \"PID: \$(cat app.pid)\"
    echo \"访问地址: http://\$(hostname -I | awk '{print \$1}'):$PORT\"
else
    echo '❌ 服务启动失败'
    cat logs/service.log
    exit 1
fi
"

if [ $? -eq 0 ]; then
    print_success "服务启动成功！"
else
    print_error "服务启动失败"
    exit 1
fi

# 8. 验证部署
print_info "8️⃣ 验证部署..."
sleep 2

# 获取服务器IP
SERVER_IP=$(ssh $SSH_HOST "hostname -I | awk '{print \$1}'")

# 测试Web服务
if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP:$PORT" | grep -q "200"; then
    print_success "Web服务响应正常"
else
    print_warning "Web服务响应异常，可能需要等待更长时间"
fi

# 9. 部署完成
print_success "🎉 部署完成！"
echo ""
echo "=========================================="
echo "  多交易所持仓可视化服务部署成功"
echo "=========================================="
echo "服务器: $SSH_HOST ($HOSTNAME)"
echo "部署目录: $REMOTE_DIR"
echo "访问地址: http://$SERVER_IP:$PORT"
echo "Python环境: 虚拟环境 (venv)"
echo ""
echo "管理命令:"
echo "  查看状态: ssh $SSH_HOST 'cd $REMOTE_DIR && kill -0 \$(cat app.pid 2>/dev/null) && echo \"运行中\" || echo \"已停止\"'"
echo "  停止服务: ssh $SSH_HOST 'cd $REMOTE_DIR && kill \$(cat app.pid) && rm app.pid'"
echo "  启动服务: ssh $SSH_HOST 'cd $REMOTE_DIR && source venv/bin/activate && nohup python app.py > logs/service.log 2>&1 & echo \$! > app.pid'"
echo "  查看日志: ssh $SSH_HOST 'tail -f $REMOTE_DIR/logs/service.log'"
echo "  运行测试: ssh $SSH_HOST 'cd $REMOTE_DIR && source venv/bin/activate && python test_client.py'"
echo ""
echo "快速重新部署:"
echo "  ./deploy.sh $SSH_HOST $PORT"
echo "=========================================="
